openapi: "3.0.3"
info:
  title: "排污许可证审核系统 API"
  description: "排污许可证审核系统的后端API接口文档"
  version: "1.0.0"
servers:
  - url: "https://permit.test52dzhp.com/ko"
  - url: "http://127.0.0.1:18019"
paths:
  /:
    get:
      description: "首页，返回Hello World!"
      responses:
        "200":
          description: "成功"
          content:
            text/plain:
              schema:
                type: "string"
              examples:
                Example#1:
                  value: "OK"

  # 聚创方舟内部管理接口
  # - POST /api/admin/login : 管理员登录
  # - POST /api/admin/key/new : 创建新的管理员密钥
  # - GET /api/admin/key/list : 获取管理员密钥列表（支持分页）
  #   查询参数：onlyActive, pagination, page, pageSize
  # - GET /api/admin/key/{id} : 获取指定管理员密钥详情
  # - PUT /api/admin/key/{id} : 更新管理员密钥信息（不可修改：code, expiresAt, province, city, district）
  # - PUT /api/admin/key/{id}/status : 更新管理员密钥状态
  # - DELETE /api/admin/key/{id} : 删除管理员密钥
  /api/admin/login:
    post:
      tags:
        - "聚创方舟内部管理"
      summary: "管理员登录"
      description: "使用管理员账号和密码进行登录，返回JWT令牌"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              required:
                - "username"
                - "password"
              properties:
                username:
                  type: "string"
                  description: "管理员用户名"
                  example: "permitAdmin"
                password:
                  type: "string"
                  description: "管理员密码"
                  example: "1_52Dzhp"
      responses:
        "200":
          description: "登录成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "Login successful"
                  data:
                    type: "object"
                    properties:
                      token:
                        type: "string"
                        description: "JWT令牌"
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                      expiresIn:
                        type: "integer"
                        description: "令牌有效期（毫秒）"
                        example: 86400000
        "401":
          description: "用户名或密码错误"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 401
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "Invalid username or password"
                  data:
                    type: "null"
        "400":
          description: "请求格式错误"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 400
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "Invalid request: ..."
                  data:
                    type: "null"

  /api/admin/key/new:
    post:
      tags:
        - "聚创方舟内部管理"
      summary: "创建新的管理员密钥"
      description: "创建新的管理员密钥（一级邀请码），返回主键ID和生成的密钥"
      security:
        - adminAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                maxCNYLimit:
                  type: "number"
                  description: "最大金额限制（元）"
                  example: 10000
                province:
                  type: "string"
                  description: "省份"
                  example: "浙江省"
                  nullable: true
                city:
                  type: "string"
                  description: "城市"
                  example: "杭州市"
                  nullable: true
                district:
                  type: "string"
                  description: "区县（可选）"
                  example: "西湖区"
                  nullable: true
                companyName:
                  type: "string"
                  description: "所属单位名称"
                  example: "浙江聚创方舟科技有限公司"
                contractNumber:
                  type: "string"
                  description: "合同号（可选）"
                  example: "HT2025001"
                  nullable: true
                contactPerson:
                  type: "string"
                  description: "联系人（可选）"
                  example: "张三"
                  nullable: true
                contactPhone:
                  type: "string"
                  description: "联系电话（可选）"
                  example: "13800138000"
                  nullable: true
                contactEmail:
                  type: "string"
                  description: "联系邮箱（可选）"
                  example: "<EMAIL>"
                  nullable: true
                expiresAt:
                  type: "integer"
                  format: "int64"
                  description: "有效期截止时间（毫秒时间戳）"
                  example: 1877777777777
      responses:
        "201":
          description: "创建成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 201
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "管理员密钥创建成功"
                  data:
                    type: "object"
                    properties:
                      id:
                        type: "integer"
                        description: "管理员密钥ID"
                        example: 1
                      code:
                        type: "string"
                        description: "生成的管理员密钥"
                        example: "ak-704a9dccf32813ec1641b363519003ec"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/admin/key/list:
    get:
      tags:
        - "聚创方舟内部管理"
      summary: "获取管理员密钥列表（支持分页）"
      description: "获取管理员密钥列表，支持分页查询和过滤。默认启用分页，可通过pagination参数控制"
      security:
        - adminAuth: []
      parameters:
        - name: "onlyActive"
          in: "query"
          description: "是否只返回有效的管理员密钥"
          required: false
          schema:
            type: "boolean"
            default: true
          example: true
        - name: "pagination"
          in: "query"
          description: "是否启用分页。设为false时返回所有数据（向后兼容）"
          required: false
          schema:
            type: "boolean"
            default: true
          example: true
        - name: "page"
          in: "query"
          description: "页码，从1开始。仅在pagination=true时有效"
          required: false
          schema:
            type: "integer"
            minimum: 1
            default: 1
          example: 1
        - name: "pageSize"
          in: "query"
          description: "每页大小，范围1-100。仅在pagination=true时有效"
          required: false
          schema:
            type: "integer"
            minimum: 1
            maximum: 100
            default: 10
          example: 10
      responses:
        "200":
          description: "获取成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "获取管理员密钥列表成功"
                  data:
                    oneOf:
                      - $ref: "#/components/schemas/PaginatedAdminKeyResponse"
                      - type: "array"
                        items:
                          $ref: "#/components/schemas/AdminKey"
                        description: "当pagination=false时返回的数组格式"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/admin/key/{id}:
    get:
      tags:
        - "聚创方舟内部管理"
      summary: "获取指定管理员密钥详情"
      description: "根据ID获取管理员密钥的详细信息"
      security:
        - adminAuth: []
      parameters:
        - name: "id"
          in: "path"
          description: "管理员密钥ID"
          required: true
          schema:
            type: "integer"
            format: "int64"
      responses:
        "200":
          description: "获取成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "获取管理员密钥详情成功"
                  data:
                    $ref: "#/components/schemas/AdminKey"
        "400":
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: "管理员密钥不存在"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

    put:
      tags:
        - "聚创方舟内部管理"
      summary: "更新管理员密钥信息"
      description: "更新指定管理员密钥的信息（不可修改：code, expiresAt, province, city, district）"
      security:
        - adminAuth: []
      parameters:
        - name: "id"
          in: "path"
          description: "管理员密钥ID"
          required: true
          schema:
            type: "integer"
            format: "int64"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                maxCNYLimit:
                  type: "number"
                  description: "最大金额限制（元）"
                  example: 20000
                status:
                  type: "string"
                  description: "状态：ACTIVE（有效）/ DISABLED（停用）"
                  enum: ["ACTIVE", "DISABLED"]
                  example: "ACTIVE"
                companyName:
                  type: "string"
                  description: "所属单位名称"
                  example: "更新后的测试公司"
                contractNumber:
                  type: "string"
                  description: "合同号（可选）"
                  example: "HT2023002-Updated"
                  nullable: true
                contactPerson:
                  type: "string"
                  description: "联系人（可选）"
                  example: "李四"
                  nullable: true
                contactPhone:
                  type: "string"
                  description: "联系电话（可选）"
                  example: "13900139000"
                  nullable: true
                contactEmail:
                  type: "string"
                  description: "联系邮箱（可选）"
                  example: "<EMAIL>"
                  nullable: true
      responses:
        "200":
          description: "更新成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "管理员密钥更新成功"
                  data:
                    $ref: "#/components/schemas/AdminKey"
        "400":
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: "管理员密钥不存在或已删除"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    delete:
      tags:
        - "聚创方舟内部管理"
      summary: "删除管理员密钥"
      description: "根据ID删除管理员密钥（软删除）"
      security:
        - adminAuth: []
      parameters:
        - name: "id"
          in: "path"
          description: "管理员密钥ID"
          required: true
          schema:
            type: "integer"
            format: "int64"
      responses:
        "200":
          description: "删除成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "管理员密钥删除成功"
                  data:
                    type: "null"
        "400":
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: "管理员密钥不存在或已删除"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/admin/key/{id}/status:
    put:
      tags:
        - "聚创方舟内部管理"
      summary: "更新管理员密钥状态"
      description: "更新指定管理员密钥的状态（启用/停用）"
      security:
        - adminAuth: []
      parameters:
        - name: "id"
          in: "path"
          description: "管理员密钥ID"
          required: true
          schema:
            type: "integer"
            format: "int64"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              required:
                - "status"
              properties:
                status:
                  type: "string"
                  description: "状态：ACTIVE（有效）/ DISABLED（停用）"
                  enum: ["ACTIVE", "DISABLED"]
                  example: "ACTIVE"
      responses:
        "200":
          description: "更新成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "管理员密钥状态更新成功"
                  data:
                    type: "null"
        "400":
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: "管理员密钥不存在或已删除"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/admin-key/login:
    post:
      tags:
        - "单位使用一级邀请码管理"
      summary: "使用管理员密钥登录"
      description: "使用管理员密钥（一级邀请码）进行登录，返回JWT令牌和管理员密钥信息"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              required:
                - "adminKeyCode"
              properties:
                adminKeyCode:
                  type: "string"
                  description: "管理员密钥"
                  example: "3301064155"
      responses:
        "200":
          description: "登录成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "登录成功"
                  data:
                    type: "object"
                    properties:
                      token:
                        type: "string"
                        description: "JWT令牌"
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                      expiresIn:
                        type: "integer"
                        description: "令牌有效期（毫秒）"
                        example: 86400000
                      id:
                        type: "integer"
                        description: "管理员密钥ID"
                        example: 1
                      code:
                        type: "string"
                        description: "管理员密钥"
                        example: "ak-704a9dccf32813ec1641b363519003ec"
                      maxTokenLimit:
                        type: "number"
                        description: "最大token限制"
                        example: 10000
                      status:
                        type: "string"
                        description: "状态"
                        example: "ACTIVE"
                      createBy:
                        type: "string"
                        description: "创建人"
                        example: "admin"
                        nullable: true
                      updateBy:
                        type: "string"
                        description: "更新人"
                        example: "admin"
                        nullable: true
                      deleted:
                        type: "boolean"
                        description: "是否已删除"
                        example: false
                      createdAt:
                        type: "integer"
                        description: "创建时间（毫秒时间戳）"
                        example: *************
                      updatedAt:
                        type: "integer"
                        description: "更新时间（毫秒时间戳）"
                        example: *************
        "400":
          description: "请求格式错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: "管理员密钥无效或已停用"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/admin-key/invitation/code/new:
    post:
      tags:
        - "单位使用一级邀请码管理"
      summary: "创建新的二级邀请码"
      description: "使用管理员密钥（一级邀请码）创建新的二级邀请码"
      security:
        - adminKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              required:
                - "userType"
                - "permissionLevel"
                - "district"
                - "status"
                - "expiresAt"
                - "remainingBalance"
                - "concurrentLimit"
              properties:
                userType:
                  type: "string"
                  description: "用户类型：COUNTY_ADMINISTRATION（县级行政机构）/ INDIVIDUAL（个人用户）"
                  enum: ["COUNTY_ADMINISTRATION", "INDIVIDUAL"]
                  example: "INDIVIDUAL"
                permissionLevel:
                  type: "string"
                  description: "权限等级：BASIC（基础）/ STANDARD（标准）/ ADVANCED（高级）/ ADMIN（管理员）"
                  enum: ["BASIC", "STANDARD", "ADVANCED", "ADMIN"]
                  example: "STANDARD"
                province:
                  type: "string"
                  description: "省份（将被管理员密钥的省份覆盖）"
                  example: "浙江省"
                city:
                  type: "string"
                  description: "城市（将被管理员密钥的城市覆盖）"
                  example: "杭州市"
                district:
                  type: "string"
                  description: "区县（必须属于管理员密钥的省市）"
                  example: "西湖区"
                status:
                  type: "string"
                  description: "状态：ACTIVE（有效）/ DISABLED（停用）/ EXPIRED（过期）"
                  enum: ["ACTIVE", "DISABLED", "EXPIRED"]
                  example: "ACTIVE"
                expiresAt:
                  type: "integer"
                  description: "过期时间（毫秒时间戳）"
                  example: 1854659456789
                remainingBalance:
                  type: "number"
                  description: "剩余可用余额（TOKEN）"
                  example: 1000
                concurrentLimit:
                  type: "integer"
                  description: "并发使用上限"
                  example: 5
                companyName:
                  type: "string"
                  description: "所属公司名称"
                  example: "测试公司"
                  nullable: true
                contactPerson:
                  type: "string"
                  description: "联系人姓名"
                  example: "张三"
                  nullable: true
                contactPhone:
                  type: "string"
                  description: "联系电话"
                  example: "13800138000"
                  nullable: true
                contactEmail:
                  type: "string"
                  description: "联系邮箱"
                  example: "<EMAIL>"
                  nullable: true
      responses:
        "201":
          description: "创建成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 201
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "邀请码创建成功"
                  data:
                    type: "object"
                    properties:
                      id:
                        type: "integer"
                        description: "邀请码ID"
                        example: 1
                      code:
                        type: "string"
                        description: "生成的邀请码"
                        example: "ABC123DEF456"
        "400":
          description: "请求格式错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: "未授权或管理员密钥无效"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/admin-key/invitation/code/list:
    get:
      tags:
        - "单位使用一级邀请码管理"
      summary: "获取当前管理员密钥创建的邀请码列表"
      description: "获取当前管理员密钥创建的所有二级邀请码列表"
      security:
        - adminKeyAuth: []
      parameters:
        - name: "onlyActive"
          in: "query"
          description: "是否只返回有效的邀请码"
          required: false
          schema:
            type: "boolean"
            default: true
      responses:
        "200":
          description: "获取成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "OK"
                  data:
                    type: "array"
                    items:
                      $ref: "#/components/schemas/InvitationCode"
        "401":
          description: "未授权或管理员密钥无效"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/admin-key/invitation/code/{id}:
    put:
      tags:
        - "单位使用一级邀请码管理"
      summary: "修改二级邀请码"
      description: "修改指定ID的二级邀请码信息"
      security:
        - adminKeyAuth: []
      parameters:
        - name: "id"
          in: "path"
          description: "邀请码ID"
          required: true
          schema:
            type: "integer"
            format: "int64"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              properties:
                userType:
                  type: "string"
                  description: "用户类型：COUNTY_ADMINISTRATION（县级行政机构）/ INDIVIDUAL（个人用户）"
                  enum: ["COUNTY_ADMINISTRATION", "INDIVIDUAL"]
                  example: "COUNTY_ADMINISTRATION"
                permissionLevel:
                  type: "string"
                  description: "权限等级：BASIC（基础）/ STANDARD（标准）/ ADVANCED（高级）/ ADMIN（管理员）"
                  enum: ["BASIC", "STANDARD", "ADVANCED", "ADMIN"]
                  example: "ADVANCED"
                province:
                  type: "string"
                  description: "省份（将被管理员密钥的省份覆盖）"
                  example: "浙江省"
                city:
                  type: "string"
                  description: "城市（将被管理员密钥的城市覆盖）"
                  example: "宁波市"
                district:
                  type: "string"
                  description: "区县（必须属于管理员密钥的省市）"
                  example: "鄞州区"
                status:
                  type: "string"
                  description: "状态：ACTIVE（有效）/ DISABLED（停用）/ EXPIRED（过期）"
                  enum: ["ACTIVE", "DISABLED", "EXPIRED"]
                  example: "ACTIVE"
                expiresAt:
                  type: "integer"
                  description: "过期时间（毫秒时间戳）"
                  example: 1654659456789
                remainingBalance:
                  type: "number"
                  description: "剩余可用余额（TOKEN）"
                  example: 2000
                concurrentLimit:
                  type: "integer"
                  description: "并发使用上限"
                  example: 10
                companyName:
                  type: "string"
                  description: "所属公司名称"
                  example: "测试公司"
                  nullable: true
                contactPerson:
                  type: "string"
                  description: "联系人姓名"
                  example: "张三"
                  nullable: true
                contactPhone:
                  type: "string"
                  description: "联系电话"
                  example: "13800138000"
                  nullable: true
                contactEmail:
                  type: "string"
                  description: "联系邮箱"
                  example: "<EMAIL>"
                  nullable: true
      responses:
        "200":
          description: "修改成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "邀请码修改成功"
                  data:
                    type: "null"
        "400":
          description: "请求格式错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: "未授权或管理员密钥无效"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: "邀请码不存在或已删除"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    delete:
      tags:
        - "单位使用一级邀请码管理"
      summary: "删除二级邀请码"
      description: "删除指定ID的二级邀请码（软删除）"
      security:
        - adminKeyAuth: []
      parameters:
        - name: "id"
          in: "path"
          description: "邀请码ID"
          required: true
          schema:
            type: "integer"
            format: "int64"
      responses:
        "200":
          description: "删除成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "邀请码删除成功"
                  data:
                    type: "null"
        "401":
          description: "未授权或管理员密钥无效"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: "邀请码不存在或已删除"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/agent/list:
    get:
      tags:
        - "Agent 相关接口(原18018 python接口)"
      summary: "获取代理列表"
      description: "返回所有在线的代理列表"
      responses:
        "200":
          description: "获取成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "获取代理列表成功"
                  data:
                    $ref: "#/components/schemas/PermitAgentListResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/agent/tags:
    get:
      tags:
        - "Agent 相关接口(原18018 python接口)"
      summary: "获取所有代理标签"
      description: "返回所有在线代理的标签列表（去重）"
      responses:
        "200":
          description: "获取成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "获取代理标签列表成功"
                  data:
                    $ref: "#/components/schemas/PermitAgentTagsResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/llm/usage:
    post:
      tags:
        - "LLM使用量(Python 后端调用, 与前端无关)"
      summary: "记录单条LLM使用量"
      description: "将单条LLM使用量记录写入数据库"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LLMUsage"
      responses:
        "201":
          description: "创建成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  id:
                    type: "integer"
                    format: "int64"
                    description: "新创建记录的ID"
                    example: 1
        "400":
          description: "请求格式错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/llm/usage/batch:
    post:
      tags:
        - "LLM使用量(Python 后端调用, 与前端无关)"
      summary: "批量记录LLM使用量"
      description: "将多条LLM使用量记录批量写入数据库"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "array"
              items:
                $ref: "#/components/schemas/LLMUsage"
      responses:
        "201":
          description: "创建成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  insertedRows:
                    type: "integer"
                    description: "成功插入的行数"
                    example: 2
        "400":
          description: "请求格式错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/sse/prepare:
    post:
      tags:
        - "Agent 相关接口(原18018 python接口)"
      summary: "准备SSE代理流程请求"
      description: "接收代理流程参数，生成requestId并存储到Redis，为后续SSE调用做准备"
      security:
        - userLLMAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              required:
                - "agent_list"
                - "storage"
                - "permit_mee_data"
              properties:
                agent_list:
                  type: "array"
                  items:
                    type: "string"
                  description: "代理ID列表"
                  example: ["1001", "1002", "1003"]
                storage:
                  type: "object"
                  required:
                    - "username"
                    - "password"
                    - "data_id"
                  properties:
                    username:
                      type: "string"
                      description: "存储系统用户名"
                      example: "APQ123456"
                    password:
                      type: "string"
                      description: "存储系统密码"
                      example: "Apq123654"
                    data_id:
                      type: "string"
                      description: "数据ID"
                      example: "e692fad4b8a64b74a902fb50d926ca94"
                permit_mee_data:
                  type: "object"
                  required:
                    - "category"
                    - "data_id"
                    - "company_name"
                    - "review_status"
                    - "submit_time"
                  properties:
                    category:
                      type: "string"
                      description: "许可证类别"
                      example: "许可证重新申请"
                    data_id:
                      type: "string"
                      description: "数据ID"
                      example: "e692fad4b8a64b74a902fb50d926ca94"
                    company_name:
                      type: "string"
                      description: "公司名称"
                      example: "浙江昊吉力针织有限公司"
                    review_status:
                      type: "string"
                      description: "审核状态"
                      example: "审批通过"
                    submit_time:
                      type: "string"
                      description: "提交时间"
                      example: "2023-07-25"
      responses:
        "200":
          description: "SSE请求准备成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "SSE请求准备完成"
                  data:
                    type: "object"
                    properties:
                      request_id:
                        type: "string"
                        description: "请求ID，用于后续SSE调用"
                        example: "550e8400-e29b-41d4-a716-446655440000"
                      message:
                        type: "string"
                        description: "提示信息"
                        example: "请使用此request_id调用SSE接口"
        "400":
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: "认证失败，需要有效的user-llm-jwt令牌"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /sse/n/run/flow:
    get:
      tags:
        - "Agent 相关接口(原18018 python接口)"
      summary: "SSE代理流程接口"
      description: "使用Server-Sent Events (SSE)并发调用多个代理进行预审核，实时流式返回每个代理的执行结果。需要先调用/api/sse/prepare接口获取request_id"
      security:
        - userLLMAuth: []
      parameters:
        - name: "request_id"
          in: "query"
          required: true
          description: "请求ID，通过/api/sse/prepare接口获取"
          schema:
            type: "string"
            example: "550e8400-e29b-41d4-a716-446655440000"
      responses:
        "200":
          description: "SSE连接建立成功，开始流式返回代理执行结果"
          content:
            text/event-stream:
              schema:
                type: "string"
                description: "SSE事件流，包含多种事件类型"
              examples:
                start_event:
                  summary: "开始事件"
                  value: |
                    event: start
                    data: {"type":"start","request_id":"550e8400-e29b-41d4-a716-446655440000","total_agents":3,"message":"预审核流程创建完毕, 共 3 个审核项, 开始预审核 ......"}
                agent_complete_event:
                  summary: "代理完成事件"
                  value: |
                    event: agent_complete
                    data: {"agent_result":"代理执行结果内容..."}
                agent_error_event:
                  summary: "代理错误事件"
                  value: |
                    event: agent_error
                    data: {"type":"agent_error","agent_id":"1001","agent_index":0,"request_id":"550e8400-e29b-41d4-a716-446655440000","error":"代理调用失败"}
                complete_event:
                  summary: "完成事件"
                  value: |
                    event: complete
                    data: {"type":"complete","request_id":"550e8400-e29b-41d4-a716-446655440000","message":"预审核已完成，结果已保存。请注意，此预审核由AI辅助完成，结果仅供参考，最终判断请以人工审核为准。"}
                error_event:
                  summary: "错误事件"
                  value: |
                    event: error
                    data: {"type":"error","request_id":"550e8400-e29b-41d4-a716-446655440000","error":"处理过程中发生错误"}
        "400":
          description: "请求参数错误"
          content:
            text/event-stream:
              schema:
                type: "string"
              examples:
                missing_request_id:
                  summary: "缺少request_id参数"
                  value: |
                    event: error
                    data: {"type":"error","request_id":"unknown","error":"request_id参数不能为空"}
                invalid_request_id:
                  summary: "无效的request_id"
                  value: |
                    event: error
                    data: {"type":"error","request_id":"invalid_id","error":"请求数据不存在或已过期，请重新调用准备接口"}
                data_format_error:
                  summary: "数据格式错误"
                  value: |
                    event: error
                    data: {"type":"error","request_id":"550e8400-e29b-41d4-a716-446655440000","error":"请求数据格式错误"}
        "401":
          description: "认证失败，需要有效的user-llm-jwt令牌"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            text/event-stream:
              schema:
                type: "string"
              example: |
                event: error
                data: {"type":"error","request_id":"550e8400-e29b-41d4-a716-446655440000","error":"服务器内部错误"}

  /api/search/permit/mee/response/list:
    get:
      tags:
        - "Agent 相关接口(原18018 python接口)"
      summary: "搜索许可证MEE响应列表"
      description: "根据用户名搜索许可证MEE响应记录列表，返回该用户的所有响应记录"
      security:
        - userLLMAuth: []
      parameters:
        - name: "username"
          in: "query"
          required: true
          description: "用户名"
          schema:
            type: "string"
            example: "test_user"
      responses:
        "200":
          description: "获取许可证MEE响应列表成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "获取许可证MEE响应列表成功"
                  data:
                    type: "object"
                    properties:
                      responses:
                        type: "array"
                        description: "许可证MEE响应列表"
                        items:
                          $ref: "#/components/schemas/PermitMeeResponseItem"
        "400":
          description: "请求参数格式错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: "认证失败，需要有效的user-llm-jwt令牌"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/search/permit/mee/response/content:
    get:
      tags:
        - "Agent 相关接口(原18018 python接口)"
      summary: "搜索许可证MEE响应详情"
      description: "根据用户名、数据ID和时间戳搜索特定的许可证MEE响应详情，包含完整的响应内容"
      security:
        - userLLMAuth: []
      parameters:
        - name: "username"
          in: "query"
          required: true
          description: "用户名"
          schema:
            type: "string"
            example: "test_user"
        - name: "data_id"
          in: "query"
          required: true
          description: "数据ID"
          schema:
            type: "string"
            example: "e692fad4b8a64b74a902fb50d926ca94"
        - name: "timestamp"
          in: "query"
          required: true
          description: "时间戳"
          schema:
            type: "string"
            example: "1234567890"
      responses:
        "200":
          description: "获取许可证MEE响应详情成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "获取许可证MEE响应详情成功"
                  data:
                    type: "object"
                    properties:
                      response:
                        $ref: "#/components/schemas/PermitMeeResponseDetail"
                        nullable: true
        "400":
          description: "请求参数格式错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: "认证失败，需要有效的user-llm-jwt令牌"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /api/user/invitation/login:
    post:
      tags:
        - "用户接口"
      summary: "用户使用二级邀请码登录"
      description: "用户使用二级邀请码登录，返回JWT令牌和邀请码的所有字段信息，同时检查并发限制"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: "object"
              required:
                - "invitationCode"
              properties:
                invitationCode:
                  type: "string"
                  description: "二级邀请码"
                  example: "ABC123DEF456"
      responses:
        "200":
          description: "登录成功"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 200
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "Login successful"
                  data:
                    type: "object"
                    properties:
                      token:
                        type: "string"
                        description: "JWT令牌"
                        example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                      expiresIn:
                        type: "integer"
                        description: "令牌有效期（毫秒）"
                        example: 86400000
                      id:
                        type: "integer"
                        description: "邀请码ID"
                        example: 1
                      code:
                        type: "string"
                        description: "邀请码"
                        example: "ABC123DEF456"
                      adminKeyId:
                        type: "integer"
                        description: "关联的管理员密钥ID"
                        example: 1
                        nullable: true
                      userType:
                        type: "string"
                        description: "用户类型"
                        example: "INDIVIDUAL"
                      permissionLevel:
                        type: "string"
                        description: "权限等级"
                        example: "STANDARD"
                      province:
                        type: "string"
                        description: "省份"
                        example: "浙江省"
                        nullable: true
                      city:
                        type: "string"
                        description: "城市"
                        example: "杭州市"
                        nullable: true
                      district:
                        type: "string"
                        description: "区县"
                        example: "西湖区"
                        nullable: true
                      regionConstraint:
                        type: "string"
                        description: "地域限制（旧版字段，将被废弃）"
                        example: "330106"
                        nullable: true
                      status:
                        type: "string"
                        description: "状态"
                        example: "ACTIVE"
                      remainingBalance:
                        type: "number"
                        description: "剩余可用余额（TOKEN）"
                        example: 1000
                      consumedAmount:
                        type: "number"
                        description: "已消耗金额"
                        example: 200
                      concurrentLimit:
                        type: "integer"
                        description: "并发使用上限"
                        example: 5
                      companyName:
                        type: "string"
                        description: "所属公司名称"
                        example: "company123"
                        nullable: true
                      contactPerson:
                        type: "string"
                        description: "联系人姓名"
                        example: "张三"
                        nullable: true
                      contactPhone:
                        type: "string"
                        description: "联系电话"
                        example: "13800138000"
                        nullable: true
                      contactEmail:
                        type: "string"
                        description: "联系邮箱"
                        example: "<EMAIL>"
                        nullable: true
                      createBy:
                        type: "string"
                        description: "创建人"
                        example: "admin"
                        nullable: true
                      updateBy:
                        type: "string"
                        description: "更新人"
                        example: "admin"
                        nullable: true
                      deleted:
                        type: "boolean"
                        description: "是否已删除"
                        example: false
                      createdAt:
                        type: "integer"
                        description: "创建时间（毫秒时间戳）"
                        example: *************
                      expiresAt:
                        type: "integer"
                        description: "过期时间（毫秒时间戳）"
                        example: 1654659456789
                      updateAt:
                        type: "integer"
                        description: "更新时间（毫秒时间戳）"
                        example: *************
        "400":
          description: "请求格式错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: "邀请码无效、已停用或已过期"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 401
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "Invalid invitation code"
                  data:
                    type: "null"
        "402":
          description: "邀请码余额不足"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 402
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "Invitation code has insufficient balance"
                  data:
                    type: "null"
        "429":
          description: "并发限制超过"
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  code:
                    type: "integer"
                    description: "状态码"
                    example: 429
                  message:
                    type: "string"
                    description: "状态信息"
                    example: "Concurrency limit exceeded"
                  data:
                    type: "null"
        "500":
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

components:
  schemas:
    AdminKey:
      type: "object"
      properties:
        id:
          type: "integer"
          format: "int64"
          description: "管理员密钥ID"
          example: 1
        code:
          type: "string"
          description: "管理员密钥"
          example: "ak-704a9dccf32813ec1641b363519003ec"
        maxCNYLimit:
          type: "number"
          description: "最大金额限制（元）"
          example: 10000
        province:
          type: "string"
          description: "省份"
          example: "浙江省"
          nullable: true
        city:
          type: "string"
          description: "城市"
          example: "杭州市"
          nullable: true
        district:
          type: "string"
          description: "区县"
          example: "西湖区"
          nullable: true
        companyName:
          type: "string"
          description: "所属单位名称"
          example: "测试公司"
        contractNumber:
          type: "string"
          description: "合同号"
          example: "HT2023001"
          nullable: true
        contactPerson:
          type: "string"
          description: "联系人"
          example: "张三"
          nullable: true
        contactPhone:
          type: "string"
          description: "联系电话"
          example: "13800138000"
          nullable: true
        contactEmail:
          type: "string"
          description: "联系邮箱"
          example: "<EMAIL>"
          nullable: true
        expiresAt:
          type: "integer"
          format: "int64"
          description: "有效期截止时间（毫秒时间戳）"
          example: *************
        status:
          type: "string"
          description: "状态：ACTIVE（有效）/ DISABLED（停用）"
          enum: ["ACTIVE", "DISABLED"]
          example: "ACTIVE"
        accountType:
          type: "string"
          description: "账号性质：TRIAL（试用账号）/ FORMAL（正式账号）"
          enum: ["TRIAL", "FORMAL"]
          example: "FORMAL"
        createBy:
          type: "string"
          description: "创建人"
          example: "admin"
          nullable: true
        updateBy:
          type: "string"
          description: "更新人"
          example: "admin"
          nullable: true
        deleted:
          type: "boolean"
          description: "是否已删除"
          example: false
        createdAt:
          type: "integer"
          format: "int64"
          description: "创建时间（毫秒时间戳）"
          example: *************
        updatedAt:
          type: "integer"
          format: "int64"
          description: "更新时间（毫秒时间戳）"
          example: *************

    ErrorResponse:
      type: "object"
      properties:
        code:
          type: "integer"
          description: "HTTP状态码"
          example: 400
        message:
          type: "string"
          description: "错误信息"
          example: "请求参数错误"
        data:
          type: "null"
          description: "数据（错误时为null）"

    PaginationInfo:
      type: "object"
      description: "分页信息"
      properties:
        page:
          type: "integer"
          description: "当前页码（从1开始）"
          example: 1
          minimum: 1
        pageSize:
          type: "integer"
          description: "每页大小"
          example: 10
          minimum: 1
          maximum: 100
        total:
          type: "integer"
          format: "int64"
          description: "总记录数"
          example: 25
          minimum: 0
        totalPages:
          type: "integer"
          description: "总页数"
          example: 3
          minimum: 0

    PaginatedAdminKeyResponse:
      type: "object"
      description: "分页的管理员密钥响应数据"
      properties:
        items:
          type: "array"
          description: "管理员密钥列表"
          items:
            $ref: "#/components/schemas/AdminKey"
        pagination:
          $ref: "#/components/schemas/PaginationInfo"

    InvitationCode:
      type: "object"
      properties:
        id:
          type: "integer"
          format: "int64"
          description: "邀请码ID"
          example: 1
        code:
          type: "string"
          description: "邀请码字符串"
          example: "ABC123DEF456"
        adminKeyId:
          type: "integer"
          format: "int64"
          description: "关联的管理员密钥ID"
          example: 1
          nullable: true
        userType:
          type: "string"
          description: "用户类型：COUNTY_ADMINISTRATION（县级行政机构）/ INDIVIDUAL（个人用户）"
          enum: ["COUNTY_ADMINISTRATION", "INDIVIDUAL"]
          example: "INDIVIDUAL"
        permissionLevel:
          type: "string"
          description: "权限等级：BASIC（基础）/ STANDARD（标准）/ ADVANCED（高级）/ ADMIN（管理员）"
          enum: ["BASIC", "STANDARD", "ADVANCED", "ADMIN"]
          example: "STANDARD"
        province:
          type: "string"
          description: "省份"
          example: "浙江省"
          nullable: true
        city:
          type: "string"
          description: "城市"
          example: "杭州市"
          nullable: true
        district:
          type: "string"
          description: "区县（必须属于管理员密钥的省市）"
          example: "西湖区"
          nullable: true
        regionConstraint:
          type: "string"
          description: "地域限制（旧版字段，将被废弃）"
          example: "330106"
          nullable: true
        status:
          type: "string"
          description: "状态：ACTIVE（有效）/ DISABLED（停用）/ EXPIRED（过期）"
          enum: ["ACTIVE", "DISABLED", "EXPIRED"]
          example: "ACTIVE"
        remainingBalance:
          type: "number"
          format: "double"
          description: "剩余可用余额（TOKEN）"
          example: 1000.0
        consumedAmount:
          type: "number"
          format: "double"
          description: "已消耗金额"
          example: 200.0
        concurrentLimit:
          type: "integer"
          description: "并发使用上限"
          example: 5
        companyName:
          type: "string"
          description: "所属公司名称"
          example: "测试公司"
          nullable: true
        contactPerson:
          type: "string"
          description: "联系人姓名"
          example: "张三"
          nullable: true
        contactPhone:
          type: "string"
          description: "联系电话"
          example: "13800138000"
          nullable: true
        contactEmail:
          type: "string"
          description: "联系邮箱"
          example: "<EMAIL>"
          nullable: true
        createBy:
          type: "string"
          description: "创建人"
          example: "admin"
          nullable: true
        updateBy:
          type: "string"
          description: "更新人"
          example: "admin"
          nullable: true
        deleted:
          type: "boolean"
          description: "是否已删除"
          example: false
        createdAt:
          type: "integer"
          format: "int64"
          description: "创建时间（毫秒时间戳）"
          example: *************
        expiresAt:
          type: "integer"
          format: "int64"
          description: "过期时间（毫秒时间戳）"
          example: 1654659456789
        updateAt:
          type: "integer"
          format: "int64"
          description: "更新时间（毫秒时间戳）"
          example: *************

    PermitAgent:
      type: "object"
      properties:
        roleId:
          type: "string"
          description: "角色ID"
          example: "agent_123"
        roleName:
          type: "string"
          description: "角色名称"
          example: "排污许可证审核助手"
        roleDescription:
          type: "string"
          description: "角色描述"
          example: "专门用于帮助审核排污许可证的AI助手"
        roleCreatedAt:
          type: "integer"
          format: "int64"
          description: "创建时间（毫秒时间戳）"
          example: *************
        roleUpdatedAt:
          type: "integer"
          format: "int64"
          description: "更新时间（毫秒时间戳）"
          example: *************
        tags:
          type: "array"
          description: "标签列表"
          items:
            type: "string"
          example: ["排污", "审核", "助手"]
        isOnline:
          type: "integer"
          description: "是否上线：1-上线，0-下线"
          enum: [0, 1]
          example: 1

    PermitAgentListResponse:
      type: "object"
      properties:
        agents:
          type: "array"
          description: "代理列表"
          items:
            $ref: "#/components/schemas/PermitAgent"
        total:
          type: "integer"
          description: "代理总数"
          example: 10

    PermitAgentTagsResponse:
      type: "object"
      properties:
        tags:
          type: "array"
          description: "标签列表"
          items:
            type: "string"
          example: ["排污", "审核", "助手", "法规", "文档"]

    LLMUsage:
      type: "object"
      properties:
        id:
          type: "integer"
          format: "int64"
          description: "唯一ID"
          example: 1
        modelName:
          type: "string"
          description: "使用的LLM模型类型"
          example: "gpt-4o"
        promptTokens:
          type: "integer"
          description: "提示词token数量"
          example: 120
        completionTokens:
          type: "integer"
          description: "补全结果token数量"
          example: 250
        totalTokens:
          type: "integer"
          description: "总token数量"
          example: 370
        timestamp:
          type: "integer"
          format: "int64"
          description: "使用时间戳（毫秒）"
          example: *************
        callBy:
          type: "string"
          description: "从哪个app的哪个用途调用"
          example: "intellij.http.test"
          nullable: true
        userId:
          type: "string"
          description: "用户ID"
          example: "user-12345"
          nullable: true
        requestId:
          type: "string"
          description: "请求ID，用于追踪同一请求的多个LLM调用"
          example: "550e8400-e29b-41d4-a716-446655440000"
          nullable: true

    SSEStartEvent:
      type: "object"
      properties:
        type:
          type: "string"
          description: "事件类型"
          example: "start"
        request_id:
          type: "string"
          description: "请求ID"
          example: "550e8400-e29b-41d4-a716-446655440000"
        total_agents:
          type: "integer"
          description: "代理总数"
          example: 3
        message:
          type: "string"
          description: "开始消息"
          example: "预审核流程创建完毕, 共 3 个审核项, 开始预审核 ......"

    SSEAgentErrorEvent:
      type: "object"
      properties:
        type:
          type: "string"
          description: "事件类型"
          example: "agent_error"
        agent_id:
          type: "string"
          description: "代理ID"
          example: "1001"
        agent_index:
          type: "integer"
          description: "代理索引"
          example: 0
        request_id:
          type: "string"
          description: "请求ID"
          example: "550e8400-e29b-41d4-a716-446655440000"
        error:
          type: "string"
          description: "错误信息"
          example: "代理调用失败"

    SSECompleteEvent:
      type: "object"
      properties:
        type:
          type: "string"
          description: "事件类型"
          example: "complete"
        request_id:
          type: "string"
          description: "请求ID"
          example: "550e8400-e29b-41d4-a716-446655440000"
        message:
          type: "string"
          description: "完成消息"
          example: "预审核已完成，结果已保存。请注意，此预审核由AI辅助完成，结果仅供参考，最终判断请以人工审核为准。"

    SSEErrorEvent:
      type: "object"
      properties:
        type:
          type: "string"
          description: "事件类型"
          example: "error"
        request_id:
          type: "string"
          description: "请求ID"
          example: "550e8400-e29b-41d4-a716-446655440000"
        error:
          type: "string"
          description: "错误信息"
          example: "处理过程中发生错误"

    PermitMeeResponseItem:
      type: "object"
      properties:
        username:
          type: "string"
          description: "用户名"
          example: "test_user"
        timestamp:
          type: "integer"
          format: "int64"
          description: "时间戳（毫秒）"
          example: *************
        data_id:
          type: "string"
          description: "数据ID"
          example: "e692fad4b8a64b74a902fb50d926ca94"
        permit_mee_data:
          type: "object"
          description: "许可证MEE数据"
          nullable: true
          example:
            category: "许可证重新申请"
            company_name: "浙江昊吉力针织有限公司"
            review_status: "审批通过"
            submit_time: "2023-07-25"
        request_id:
          type: "string"
          description: "请求ID"
          nullable: true
          example: "550e8400-e29b-41d4-a716-446655440000"

    PermitMeeResponseDetail:
      type: "object"
      properties:
        username:
          type: "string"
          description: "用户名"
          example: "test_user"
        timestamp:
          type: "integer"
          format: "int64"
          description: "时间戳（毫秒）"
          example: *************
        data_id:
          type: "string"
          description: "数据ID"
          example: "e692fad4b8a64b74a902fb50d926ca94"
        permit_mee_data:
          type: "object"
          description: "许可证MEE数据"
          nullable: true
          example:
            category: "许可证重新申请"
            company_name: "浙江昊吉力针织有限公司"
            review_status: "审批通过"
            submit_time: "2023-07-25"
        request_id:
          type: "string"
          description: "请求ID"
          nullable: true
          example: "550e8400-e29b-41d4-a716-446655440000"
        full_response:
          type: "object"
          description: "完整响应内容"
          nullable: true
          example:
            agent_results:
              - agent_id: "1001"
                result: "审核通过"
                details: "企业信息完整，符合排污许可证申请要求"
            summary: "整体审核结果良好"

  securitySchemes:
    adminAuth:
      type: "http"
      scheme: "bearer"
      bearerFormat: "JWT"
      description: "管理员JWT认证"
    adminKeyAuth:
      type: "http"
      scheme: "bearer"
      bearerFormat: "JWT"
      description: "管理员密钥JWT认证"
    userAuth:
      type: "http"
      scheme: "bearer"
      bearerFormat: "JWT"
      description: "用户JWT认证"
    userLLMAuth:
      type: "http"
      scheme: "bearer"
      bearerFormat: "JWT"
      description: "用户LLM功能JWT认证"