package com.dzhp.permit.services

import com.dzhp.permit.models.LLMUsage
import com.dzhp.permit.service.LLMUsageService
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import kotlinx.coroutines.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import kotlin.coroutines.CoroutineContext

/**
 * N8N执行数据结构
 */
@Serializable
data class N8NExecution(
    val id: String,
    val finished: Boolean,
    val mode: String,
    val retryOf: String? = null,
    val retrySuccessId: String? = null,
    val startedAt: String,
    val stoppedAt: String,
    val workflowId: String,
    val waitTill: String? = null,
    val data: N8NExecutionData? = null
)

/**
 * N8N执行列表响应
 */
@Serializable
data class N8NExecutionListResponse(
    val data: List<N8NExecution>,
    val nextCursor: String? = null
)

/**
 * N8N执行详细数据
 */
@Serializable
data class N8NExecutionData(
    val resultData: kotlinx.serialization.json.JsonElement? = null
)

/**
 * N8N使用量监控服务
 * 每分钟检查N8N执行记录，提取LLM使用量信息并记录到数据库
 */
class N8NUsageService(
    private val application: Application
) : CoroutineScope {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val llmUsageService = LLMUsageService(application)

    // 协程上下文
    private val job = SupervisorJob()
    override val coroutineContext: CoroutineContext = Dispatchers.IO + job

    // 定时器
    private val scheduler = Executors.newSingleThreadScheduledExecutor { r ->
        Thread(r, "N8N-Usage-Monitor").apply {
            isDaemon = true
        }
    }

    // N8N API配置
    private val n8nBaseUrl: String
        get() = application.environment.config.property("api.n8n.url").getString()
    private val n8nApiKey: String
        get() = application.environment.config.property("api.n8n.apiKey").getString()
    private val n8nWorkflowId: String
        get() = application.environment.config.property("api.n8n.workflowId").getString()

    // HTTP客户端
    private val httpClient = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
                prettyPrint = false
            })
        }
        engine {
            requestTimeout = 30000 // 30秒超时
        }
    }

    // Token使用量正则表达式
    private val tokenUsageRegex = Regex(
        """"tokenUsage":\s*\{\s*"completionTokens":\s*(\d+),\s*"promptTokens":\s*(\d+),\s*"totalTokens":\s*(\d+)\s*\}"""
    )
    private val requestIdRegex = Regex(""""request_id":\s*"([^"]+)"""")
    private val userIdRegex = Regex(""""user_id":\s*"([^"]+)"""")

    /**
     * 启动N8N使用量监控
     */
    fun startMonitoring() {
        logger.info("启动N8N使用量监控服务，每分钟检查一次")

        // 每分钟执行一次检查
        scheduler.scheduleAtFixedRate({
            try {
                runBlocking {
                    checkRecentExecutions()
                }
            } catch (e: Exception) {
                logger.error("N8N使用量检查失败", e)
            }
        }, 0, 1, TimeUnit.MINUTES)
    }

    /**
     * 停止监控服务
     */
    fun stopMonitoring() {
        logger.info("停止N8N使用量监控服务")
        scheduler.shutdown()
        job.cancel()
        httpClient.close()
    }

    /**
     * 检查最近一分钟内的执行记录
     */
    private suspend fun checkRecentExecutions() {
        logger.debug("开始检查最近一分钟内的N8N执行记录")

        val oneMinuteAgo = System.currentTimeMillis() - 60000 // 一分钟前的时间戳
        val executionIds = mutableListOf<String>()
        var cursor: String? = null

        do {
            try {
                // 获取执行列表
                val response = getExecutionList(cursor)

                // 检查每个执行记录的停止时间
                for (execution in response.data) {
                    val stoppedAtTime = parseISODateTime(execution.stoppedAt)

                    if (stoppedAtTime >= oneMinuteAgo) {
                        // 在过去一分钟内停止的执行
                        executionIds.add(execution.id)
                        logger.debug("发现最近执行记录: ${execution.id}, 停止时间: ${execution.stoppedAt}")
                    } else {
                        // 如果当前记录已经超过一分钟，后续记录也会超过，可以停止检查
                        logger.debug("执行记录 ${execution.id} 停止时间超过一分钟，停止检查")
                        break
                    }
                }

                // 检查是否需要继续分页
                cursor = response.nextCursor
                if (cursor != null && response.data.isNotEmpty()) {
                    val lastExecution = response.data.last()
                    val lastStoppedTime = parseISODateTime(lastExecution.stoppedAt)
                    if (lastStoppedTime < oneMinuteAgo) {
                        // 最后一条记录已经超过一分钟，不需要继续分页
                        cursor = null
                    }
                }

            } catch (e: Exception) {
                logger.error("获取N8N执行列表失败", e)
                break
            }
        } while (cursor != null)

        logger.info("发现 ${executionIds.size} 个最近执行记录，开始提取使用量信息")

        // 处理每个执行记录
        for (executionId in executionIds) {
            try {
                processExecution(executionId)
            } catch (e: Exception) {
                logger.error("处理执行记录 $executionId 失败", e)
            }
        }
    }

    /**
     * 获取N8N执行列表
     */
    private suspend fun getExecutionList(cursor: String? = null): N8NExecutionListResponse {
        val url = buildString {
            append("$n8nBaseUrl/api/v1/executions")
            append("?workflowId=$n8nWorkflowId")
            append("&status=success")
            append("&includeData=false")
            append("&limit=100")
            if (cursor != null) {
                append("&cursor=$cursor")
            }
        }

        logger.debug("请求N8N执行列表: $url")

        val response = httpClient.get(url) {
            header("X-N8N-API-KEY", n8nApiKey)
            header("Accept", "application/json")
        }

        return response.body<N8NExecutionListResponse>()
    }

    /**
     * 获取单个执行的详细信息
     */
    private suspend fun getExecutionDetail(executionId: String): N8NExecution {
        val url = "$n8nBaseUrl/api/v1/executions/$executionId?includeData=true"

        logger.debug("请求N8N执行详情: $url")

        val response = httpClient.get(url) {
            header("X-N8N-API-KEY", n8nApiKey)
            header("Accept", "application/json")
        }

        return response.body<N8NExecution>()
    }

    /**
     * 处理单个执行记录
     */
    private suspend fun processExecution(executionId: String) {
        logger.debug("开始处理执行记录: $executionId")

        try {
            // 获取执行详情
            val execution = getExecutionDetail(executionId)

            // 将执行数据转换为JSON字符串进行正则匹配
            val executionJson = Json.encodeToString(N8NExecution.serializer(), execution)

            // 提取token使用量信息
            val tokenUsageMatches = tokenUsageRegex.findAll(executionJson)
            val requestIdMatches = requestIdRegex.findAll(executionJson)
            val userIdMatches = userIdRegex.findAll(executionJson)

            // 收集所有匹配的数据
            val requestIds = requestIdMatches.map { it.groupValues[1] }.toList()
            val userIds = userIdMatches.map { it.groupValues[1] }.toList()

            var recordCount = 0

            // 处理每个token使用量记录
            tokenUsageMatches.forEachIndexed { index, match ->
                try {
                    val completionTokens = match.groupValues[1].toInt()
                    val promptTokens = match.groupValues[2].toInt()
                    val totalTokens = match.groupValues[3].toInt()

                    // 尝试匹配对应的requestId和userId
                    val requestId = requestIds.getOrNull(index)
                    val userId = userIds.getOrNull(index)

                    if (requestId != null && userId != null) {
                        // 创建LLM使用记录
                        val llmUsage = LLMUsage(
                            modelName = "n8n-llm", // 默认模型名称，可以根据需要调整
                            promptTokens = promptTokens,
                            completionTokens = completionTokens,
                            totalTokens = totalTokens,
                            timestamp = System.currentTimeMillis(),
                            callBy = "n8n-workflow",
                            userId = userId,
                            requestId = requestId
                        )

                        // 记录到数据库
                        val recordId = llmUsageService.recordUsage(llmUsage)
                        recordCount++

                        logger.info("记录LLM使用量: executionId=$executionId, recordId=$recordId, userId=$userId, requestId=$requestId, tokens=$totalTokens")
                    } else {
                        logger.warn("执行记录 $executionId 中的token使用量缺少requestId或userId信息")
                    }
                } catch (e: Exception) {
                    logger.error("处理执行记录 $executionId 中的token使用量失败", e)
                }
            }

            logger.info("执行记录 $executionId 处理完成，共记录 $recordCount 条LLM使用量")

        } catch (e: Exception) {
            logger.error("获取执行记录 $executionId 详情失败", e)
        }
    }

    /**
     * 解析ISO 8601日期时间字符串为毫秒时间戳
     */
    private fun parseISODateTime(isoDateTime: String): Long {
        return try {
            // 简单的ISO 8601解析，假设格式为 "2025-05-28T08:21:32.256Z"
            val cleanDateTime = isoDateTime.replace("Z", "+00:00")
            java.time.Instant.parse(isoDateTime).toEpochMilli()
        } catch (e: Exception) {
            logger.warn("解析日期时间失败: $isoDateTime", e)
            0L
        }
    }
}