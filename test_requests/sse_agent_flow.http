### 测试SSE代理流程 - 两步式方案

### 第一步：准备SSE请求（POST接口）
POST http://localhost:18019/api/sse/prepare
Content-Type: application/json
Authorization: Bearer {{user_llm_jwt_token}}

{
  "agent_list": ["agent1", "agent2", "agent3"],
  "storage": {
    "username": "test_user",
    "password": "test_password",
    "data_id": "test_data_123"
  },
  "permit_mee_data": {
    "category": "环保许可",
    "data_id": "test_data_123",
    "company_name": "测试公司",
    "review_status": "待审核",
    "submit_time": "2024-01-01 10:00:00"
  }
}

### 第二步：使用返回的request_id调用SSE接口
### 注意：需要先执行上面的POST请求，获取request_id，然后替换下面URL中的{request_id}
GET http://localhost:18019/sse/n/run/flow?request_id={request_id}
Authorization: Bearer {{user_llm_jwt_token}}
Accept: text/event-stream

### 测试错误情况：缺少request_id参数
GET http://localhost:18019/sse/n/run/flow
Authorization: Bearer {{user_llm_jwt_token}}
Accept: text/event-stream

### 测试错误情况：无效的request_id
GET http://localhost:18019/sse/n/run/flow?request_id=invalid_request_id
Authorization: Bearer {{user_llm_jwt_token}}
Accept: text/event-stream

### 测试错误情况：POST接口缺少必要字段
POST http://localhost:18019/api/sse/prepare
Content-Type: application/json
Authorization: Bearer {{user_llm_jwt_token}}

{
  "agent_list": [],
  "storage": {
    "username": "",
    "password": "",
    "data_id": ""
  },
  "permit_mee_data": {
    "category": "",
    "data_id": "",
    "company_name": "",
    "review_status": "",
    "submit_time": ""
  }
}
